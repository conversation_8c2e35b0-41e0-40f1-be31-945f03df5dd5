{% load static %}
{% load i18n %}
{% load image_utils %}
<!DOCTYPE html>
<html lang="{{ current_language|default:'ar' }}" dir="{{ text_direction|default:'rtl' }}" data-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="ElDawliya HR - Modern Human Resources Management System">
    <meta name="author" content="ElDawliya Systems">
    <title>{% block title %}نظام إدارة الموارد البشرية - الدولية{% endblock %}</title>

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="{% static 'images/favicon.ico' %}">

    <!-- Google Fonts - Cairo Primary -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">

    <!-- Bootstrap 5 RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">

    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Chart.js for Dashboard Analytics -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <!-- Modern Design System -->
    <link rel="stylesheet" href="{% static 'css/style.css' %}">

    <!-- HR Module Specific Styles -->
    <link rel="stylesheet" href="{% static 'Hr/css/hr_clean.css' %}">

    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="loading-overlay d-none">
        <div class="loading-spinner">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">جاري التحميل...</span>
            </div>
            <div class="loading-text mt-3">جاري التحميل...</div>
        </div>
    </div>

    <div class="app-container">
        <!-- Mobile Menu Overlay -->
        <div class="mobile-overlay" id="mobileOverlay"></div>

        <!-- Sidebar Navigation -->
        <aside class="sidebar" id="sidebar">
            <!-- Brand Section -->
            <div class="sidebar-brand">
                <div class="sidebar-brand-icon">
                    <i class="fas fa-building text-primary"></i>
                </div>
                <div class="sidebar-brand-text">
                    <h1 class="sidebar-brand-title">الدولية</h1>
                    <p class="sidebar-brand-subtitle">الموارد البشرية</p>
                </div>
                <button class="sidebar-toggle d-lg-none" id="sidebarClose">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <!-- User Profile Section -->
            <div class="sidebar-user-profile">
                <div class="user-avatar">
                    <i class="fas fa-user-circle"></i>
                </div>
                <div class="user-info">
                    <div class="user-name">{{ user.get_full_name|default:user.username }}</div>
                    <div class="user-role">مدير الموارد البشرية</div>
                </div>
            </div>

            <!-- Navigation Menu -->
            <nav class="nav-menu">
                <ul class="nav-menu-list">
                    <!-- Dashboard -->
                    <li class="nav-item">
                        <a href="{% url 'Hr:dashboard' %}" class="nav-link {% if request.resolver_match.url_name == 'dashboard' %}active{% endif %}">
                            <i class="nav-icon fas fa-tachometer-alt"></i>
                            <span class="nav-text">لوحة التحكم</span>
                        </a>
                    </li>

                    <!-- Employee Management -->
                    <li class="nav-item has-submenu">
                        <a href="#" class="nav-link submenu-toggle {% if 'employees' in request.resolver_match.namespace|default:'' %}active{% endif %}">
                            <i class="nav-icon fas fa-users"></i>
                            <span class="nav-text">إدارة الموظفين</span>
                            <i class="submenu-arrow fas fa-chevron-down"></i>
                        </a>
                        <ul class="nav-submenu">
                            <li><a href="{% url 'Hr:employees:list' %}" class="nav-sublink">قائمة الموظفين</a></li>
                            <li><a href="{% url 'Hr:employees:create' %}" class="nav-sublink">إضافة موظف</a></li>
                            <li><a href="{% url 'Hr:employees:employee_search' %}" class="nav-sublink">البحث المتقدم</a></li>
                        </ul>
                    </li>

                    <!-- Department Management -->
                    <li class="nav-item has-submenu">
                        <a href="#" class="nav-link submenu-toggle {% if 'departments' in request.resolver_match.namespace|default:'' %}active{% endif %}">
                            <i class="nav-icon fas fa-sitemap"></i>
                            <span class="nav-text">إدارة الأقسام</span>
                            <i class="submenu-arrow fas fa-chevron-down"></i>
                        </a>
                        <ul class="nav-submenu">
                            <li><a href="{% url 'Hr:departments:department_list' %}" class="nav-sublink">قائمة الأقسام</a></li>
                            <li><a href="{% url 'Hr:departments:department_create' %}" class="nav-sublink">إضافة قسم</a></li>
                        </ul>
                    </li>

                    <!-- Job Management -->
                    <li class="nav-item">
                        <a href="{% url 'Hr:jobs:job_list' %}" class="nav-link {% if 'jobs' in request.resolver_match.namespace|default:'' %}active{% endif %}">
                            <i class="nav-icon fas fa-briefcase"></i>
                            <span class="nav-text">الوظائف</span>
                        </a>
                    </li>

                    <!-- Attendance Management -->
                    <li class="nav-item has-submenu">
                        <a href="#" class="nav-link submenu-toggle {% if 'attendance' in request.resolver_match.namespace|default:'' %}active{% endif %}">
                            <i class="nav-icon fas fa-clock"></i>
                            <span class="nav-text">الحضور والانصراف</span>
                            <i class="submenu-arrow fas fa-chevron-down"></i>
                        </a>
                        <ul class="nav-submenu">
                            <li><a href="{% url 'Hr:attendance:attendance_record_list' %}" class="nav-sublink">سجلات الحضور</a></li>
                            <li><a href="{% url 'Hr:attendance:zk_device_connection' %}" class="nav-sublink">أجهزة البصمة</a></li>
                            <li><a href="{% url 'Hr:attendance:attendance_rule_list' %}" class="nav-sublink">قواعد الحضور</a></li>
                        </ul>
                    </li>

                    <!-- Payroll Management -->
                    <li class="nav-item has-submenu">
                        <a href="#" class="nav-link submenu-toggle">
                            <i class="nav-icon fas fa-calculator"></i>
                            <span class="nav-text">الرواتب</span>
                            <i class="submenu-arrow fas fa-chevron-down"></i>
                        </a>
                        <ul class="nav-submenu">
                            <li><a href="{% url 'Hr:payroll_calculate' %}" class="nav-sublink">حساب الرواتب</a></li>
                            <li><a href="{% url 'Hr:payroll_period_list' %}" class="nav-sublink">فترات الرواتب</a></li>
                        </ul>
                    </li>

                    <!-- Reports -->
                    <li class="nav-item">
                        <a href="{% url 'Hr:reports:report_list' %}" class="nav-link {% if 'reports' in request.resolver_match.namespace|default:'' %}active{% endif %}">
                            <i class="nav-icon fas fa-chart-bar"></i>
                            <span class="nav-text">التقارير</span>
                        </a>
                    </li>

                    <!-- Analytics -->
                    <li class="nav-item">
                        <a href="{% url 'Hr:analytics:analytics_dashboard' %}" class="nav-link {% if 'analytics' in request.resolver_match.namespace|default:'' %}active{% endif %}">
                            <i class="nav-icon fas fa-chart-line"></i>
                            <span class="nav-text">التحليلات</span>
                        </a>
                    </li>
                </ul>
            </nav>

            <!-- Sidebar Footer -->
            <div class="sidebar-footer">
                <div class="theme-toggle">
                    <button class="btn btn-outline-secondary btn-sm w-100" id="sidebarThemeToggle">
                        <i class="fas fa-moon me-2"></i>
                        <span>الوضع الليلي</span>
                    </button>
                </div>
            </div>
        </aside>

        <!-- Main Content Area -->
        <div class="main-content">
            <!-- Top Navigation Bar -->
            <nav class="navbar">
                <div class="navbar-left">
                    <button class="mobile-menu-toggle" id="mobileMenuToggle">
                        <i class="fas fa-bars"></i>
                    </button>
                    <div class="navbar-title-section">
                        <h1 class="navbar-title">{% block page_title %}نظام الموارد البشرية{% endblock %}</h1>
                        <p class="navbar-subtitle">{% block page_subtitle %}إدارة شاملة للموارد البشرية{% endblock %}</p>
                    </div>
                </div>

                <div class="navbar-right">
                    <!-- Notifications -->
                    <div class="navbar-notifications">
                        <button class="btn btn-link position-relative" id="notificationsToggle">
                            <i class="fas fa-bell"></i>
                            <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">
                                3
                                <span class="visually-hidden">إشعارات جديدة</span>
                            </span>
                        </button>
                    </div>

                    <!-- Theme Toggle -->
                    <button id="themeToggle" class="btn btn-link theme-toggle" title="تبديل الوضع الليلي/النهاري">
                        <i id="themeIcon" class="fas fa-moon"></i>
                    </button>

                    <!-- User Dropdown -->
                    <div class="dropdown">
                        <button class="btn btn-link dropdown-toggle navbar-user" type="button" id="userDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                            <div class="navbar-user-avatar">
                                {{ request.user.get_full_name.0|default:request.user.username.0|upper }}
                            </div>
                            <div class="navbar-user-info d-none d-md-block">
                                <div class="navbar-user-name">{{ request.user.get_full_name|default:request.user.username }}</div>
                                <div class="navbar-user-role">مدير الموارد البشرية</div>
                            </div>
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="#"><i class="fas fa-user me-2"></i>الملف الشخصي</a></li>
                            <li><a class="dropdown-item" href="#"><i class="fas fa-cog me-2"></i>الإعدادات</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item text-danger" href="{% url 'accounts:logout' %}"><i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج</a></li>
                        </ul>
                    </div>
                </div>
            </nav>

            <!-- Breadcrumb Navigation -->
            <nav aria-label="breadcrumb" class="breadcrumb-nav">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{% url 'Hr:dashboard' %}"><i class="fas fa-home"></i> الرئيسية</a></li>
                    {% block breadcrumb %}{% endblock %}
                </ol>
            </nav>

            <!-- Content Container -->
            <div class="content-container">
                {% block content %}{% endblock %}
            </div>
        </div>
    </div>

    <!-- Bootstrap 5 JavaScript Bundle -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Component Library JavaScript -->
    <script src="{% static 'js/components.js' %}"></script>

    <!-- HR Module JavaScript -->
    <script src="{% static 'Hr/js/hr_main.js' %}"></script>

    <!-- Modern Base Template JavaScript -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Theme Management
            const themeToggle = document.getElementById('themeToggle');
            const themeIcon = document.getElementById('themeIcon');
            const sidebarThemeToggle = document.getElementById('sidebarThemeToggle');
            const htmlElement = document.documentElement;

            // Load saved theme
            const savedTheme = localStorage.getItem('theme') || 'light';
            htmlElement.setAttribute('data-theme', savedTheme);
            updateThemeIcon(savedTheme);

            // Theme toggle event listeners
            [themeToggle, sidebarThemeToggle].forEach(toggle => {
                if (toggle) {
                    toggle.addEventListener('click', () => {
                        const currentTheme = htmlElement.getAttribute('data-theme');
                        const newTheme = currentTheme === 'light' ? 'dark' : 'light';

                        htmlElement.setAttribute('data-theme', newTheme);
                        localStorage.setItem('theme', newTheme);
                        updateThemeIcon(newTheme);
                    });
                }
            });

            function updateThemeIcon(theme) {
                const iconClass = theme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
                const iconText = theme === 'dark' ? 'الوضع النهاري' : 'الوضع الليلي';

                if (themeIcon) themeIcon.className = iconClass;
                if (sidebarThemeToggle) {
                    const icon = sidebarThemeToggle.querySelector('i');
                    const text = sidebarThemeToggle.querySelector('span');
                    if (icon) icon.className = iconClass + ' me-2';
                    if (text) text.textContent = iconText;
                }
            }

            // Mobile Navigation
            const mobileMenuToggle = document.getElementById('mobileMenuToggle');
            const sidebarClose = document.getElementById('sidebarClose');
            const sidebar = document.getElementById('sidebar');
            const mobileOverlay = document.getElementById('mobileOverlay');

            function toggleSidebar() {
                sidebar.classList.toggle('show');
                mobileOverlay.classList.toggle('show');
                document.body.classList.toggle('sidebar-open');
            }

            function closeSidebar() {
                sidebar.classList.remove('show');
                mobileOverlay.classList.remove('show');
                document.body.classList.remove('sidebar-open');
            }

            if (mobileMenuToggle) {
                mobileMenuToggle.addEventListener('click', toggleSidebar);
            }

            if (sidebarClose) {
                sidebarClose.addEventListener('click', closeSidebar);
            }

            if (mobileOverlay) {
                mobileOverlay.addEventListener('click', closeSidebar);
            }

            // Submenu Toggle
            const submenuToggles = document.querySelectorAll('.submenu-toggle');
            submenuToggles.forEach(toggle => {
                toggle.addEventListener('click', function(e) {
                    e.preventDefault();
                    const parentItem = this.closest('.nav-item');
                    const submenu = parentItem.querySelector('.nav-submenu');
                    const arrow = this.querySelector('.submenu-arrow');

                    if (submenu) {
                        submenu.classList.toggle('show');
                        arrow.classList.toggle('rotated');
                        parentItem.classList.toggle('expanded');
                    }
                });
            });

            // Loading Overlay Management
            window.showLoading = function() {
                document.getElementById('loadingOverlay').classList.remove('d-none');
            };

            window.hideLoading = function() {
                document.getElementById('loadingOverlay').classList.add('d-none');
            };

            // Auto-hide loading on page load
            setTimeout(() => {
                hideLoading();
            }, 500);

            // Notifications Toggle
            const notificationsToggle = document.getElementById('notificationsToggle');
            if (notificationsToggle) {
                notificationsToggle.addEventListener('click', function() {
                    // Add notification panel toggle logic here
                    console.log('Notifications clicked');
                });
            }
        });
    </script>

    {% block extra_js %}{% endblock %}
</body>
</html>
